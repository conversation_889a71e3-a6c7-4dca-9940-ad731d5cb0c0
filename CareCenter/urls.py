from django.contrib import admin
from django.urls import path, include
urlpatterns = [
    path('admin/', admin.site.urls),
    path('permission/', include('permissions.urls')),
    path('user/', include('user.urls')),
    path('customer-service/', include('customer_service.core_records.urls.maternal_urls')),
    path('customer-service/', include('customer_service.core_records.urls.baby_urls')),
    path('customer-service/', include('customer_service.core_records.urls.maternity_admission_urls')),
    path('customer-service/', include('customer_service.room.urls')),
    path('customer-service/',include('customer_service.activity.urls')),
    path('customer-service/',include('customer_service.diet.urls')),
    path('customer-service/',include('customer_service.health_education.urls')),
    path('customer-service/', include('customer_service.visitor.urls')),
    path('maternity-center/',include('maternity_center.urls')),
    path('customer-service/',include('customer_service.medical_referral.urls')),
    path('customer-service/',include('customer_service.outing_management.urls')),
    path('customer-service/',include('customer_service.room_change.urls')),
    path('customer-service/',include('customer_service.disinfection.urls')),
    path('organizational-management/',include('organizational_management.feedback.urls')),
    path('organizational-management/',include('organizational_management.questionnaire.urls')),
    path('organizational-management/',include('organizational_management.infection_environment.urls')),
    path('organizational-management/',include('organizational_management.staff_schedule.urls')),
    path('organizational-management/',include('organizational_management.charge.urls')),
    path('organizational-management/',include('organizational_management.equipment.urls')),
    path('wx-api/maternity/',include('wx_mom.urls')),
    path('file/',include('file.urls')),
    path('message/', include('message.urls')),
    path('overview/',include('overview.urls')),
    path('audit/', include('audit_log.urls')),
    path('changelog/', include('changelog.urls')),

]
from django.conf import settings
from django.conf.urls.static import static

urlpatterns += static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)

if settings.DEBUG:
    urlpatterns += [
        path('silk/', include('silk.urls')),
    ]

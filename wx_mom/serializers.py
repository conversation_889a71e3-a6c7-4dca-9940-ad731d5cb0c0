from datetime import date

from rest_framework import serializers

from core.build_record import build_feeding_data, build_single_baby_trend
from core.enum import GenderEnum
from core.parse_time import ShanghaiFriendlyDateTimeField
from customer_service.core_records.models.baby import Newborn, NewbornCareOneRecord, NewbornCareTwoRecord
from customer_service.core_records.serializers.baby import NewbornDailyRequiredRecordDetailSerializer
from customer_service.visitor.models import WechatAppVisitAppointment
from organizational_management.feedback.models import MaternityFeedback
from organizational_management.feedback.serializers import FeedbackProcessRecordSerializer
from wx_mom.models import HealthKnowledge, PublicHealthKnowledge, WxMaternityMessage


# 新生儿护理记录单（1）
class WxNewbornCareOneRecordSerializer(serializers.ModelSerializer):
    
    creator_name = serializers.SerializerMethodField()
    
    class Meta:
        model = NewbornCareOneRecord
        fields = ['record_id','record_date','weight','length','temperature','guidance','creator_name']
        
    def get_creator_name(self,obj):
        return obj.caregiver_signature if obj.caregiver_signature else "N/A"

# 新生儿护理记录单（2）
class WxNewbornCareTwoRecordSerializer(serializers.ModelSerializer):
    
    creator_name = serializers.SerializerMethodField()
    
    class Meta:
        model = NewbornCareTwoRecord
        fields = ['record_id','record_date','jaundice_value','skin_jaundice','vomiting','limb_tone','creator_name']

    def get_creator_name(self,obj):
        return obj.caregiver_signature if obj.caregiver_signature else "N/A"
    
class WxBabyListdViewSerializer(serializers.ModelSerializer):
    # 性别
    gender = serializers.SerializerMethodField()
    # 出生天数
    days_since_birth = serializers.SerializerMethodField()
    # 出生时间
    birth_time = ShanghaiFriendlyDateTimeField()
    # 基本指标
    basic_data = serializers.SerializerMethodField()
    # 喂养记录
    feeding_data = serializers.SerializerMethodField()
    # 每日护理记录
    daily_care_data = serializers.SerializerMethodField()
    # 每日体格检查
    physical_examination_data = serializers.SerializerMethodField()
    # 趋势数据
    trend_data = serializers.SerializerMethodField()

    class Meta:
        model = Newborn
        fields = ['nid','name','gender','birth_time','days_since_birth','basic_data','feeding_data','daily_care_data','physical_examination_data','trend_data']

    def get_gender(self,obj):
        return GenderEnum(obj.gender).label if obj.gender else "N/A"

    def get_days_since_birth(self,obj):
        birth_date = obj.birth_time.date()
        current_date = date.today()
        days_since_birth = (current_date - birth_date).days
        return days_since_birth
    
    def get_basic_data(self,obj):

        daily_required_records = obj.newborn_daily_required.all()

        if not daily_required_records:
            return None

        daily_required = daily_required_records[0]

        return NewbornDailyRequiredRecordDetailSerializer(daily_required).data
    
    def get_feeding_data(self,obj):
        feeding_records = obj.newborn_feeding_records.all()
        if not feeding_records: 
            return None
        return build_feeding_data(feeding_records,single=True)

    def get_trend_data(self,obj):
        return build_single_baby_trend(obj.newborn_daily_required.all())

    def get_daily_care_data(self,obj):

        daily_care_records = obj.newborn_care_one_records.all()

        if not daily_care_records:
            return None

        daily_care_record = daily_care_records[0]

        return WxNewbornCareOneRecordSerializer(daily_care_record).data
    
    def get_physical_examination_data(self,obj):

        physical_examination_records = obj.newborn_care_two_records.all()

        if not physical_examination_records:
            return None

        physical_examination_record = physical_examination_records[0]

        return WxNewbornCareTwoRecordSerializer(physical_examination_record).data
    
    

# 意见反馈列表序列化器
class WxFeedbackListSerializer(serializers.ModelSerializer):
    
    # 反馈时间
    feedback_time = ShanghaiFriendlyDateTimeField()
    
    # 反馈类型
    feedback_type_display = serializers.SerializerMethodField()
    
    # 处理状态
    status_display = serializers.SerializerMethodField()
    
    
    class Meta:
        model = MaternityFeedback
        fields = ['fid','feedback_time','feedback_type','feedback_type_display','status','status_display','content']
        
    
    
    def get_feedback_type_display(self,obj):
        return obj.get_feedback_type_display()
    
    def get_status_display(self,obj):
        return obj.get_status_display()
    
    

# 意见反馈详情序列化器
class WxFeedbackDetailSerializer(serializers.ModelSerializer):
    
    # 反馈时间
    feedback_time = ShanghaiFriendlyDateTimeField()
    
    # 反馈类型
    feedback_type_display = serializers.SerializerMethodField()
    
    # 处理状态
    status_display = serializers.SerializerMethodField()
    
    # 创建时间
    created_at = ShanghaiFriendlyDateTimeField()
    
    # 更新时间
    updated_at = ShanghaiFriendlyDateTimeField()
    
    # 反馈处理记录
    process_records = FeedbackProcessRecordSerializer(many=True, read_only=True)
    
    
    class Meta:
        model = MaternityFeedback
        fields = [
            'fid',
            'feedback_time',
            'feedback_type',
            'feedback_type_display',
            'status',
            'status_display',
            'content',
            'contact_info',
            'process_records',  
            'created_at',
            'updated_at',
        ]

    def get_maternity_info(self, obj):
        return f'{obj.maternity_admission.maternity.name if obj.maternity_admission.maternity else '-'} （{obj.maternity_admission.room.room_number if obj.maternity_admission.room else '-'}）'

    def get_feedback_type_display(self, obj):
        return obj.get_feedback_type_display()
    
    def get_status_display(self, obj):
        return obj.get_status_display()
        
        
# 意见反馈创建序列化器
class WxFeedbackCreateSerializer(serializers.ModelSerializer):
    
    class Meta:
        model = MaternityFeedback
        fields = ['content','feedback_type','contact_info','maternity_admission','maternity_center']
        

# 意见反馈更新序列化器
class WxFeedbackUpdateSerializer(serializers.ModelSerializer):
    
    class Meta:
        model = MaternityFeedback
        fields = ['content','feedback_type','contact_info']
        


# 小程序预约参观列表序列化器
class WechatAppVisitListSerializer(serializers.ModelSerializer):
    
    # 参观时间
    visit_time = ShanghaiFriendlyDateTimeField()
    # 预约状态
    appointment_status_display = serializers.SerializerMethodField()
    # 创建时间
    created_at = ShanghaiFriendlyDateTimeField()
    
    class Meta:
        model = WechatAppVisitAppointment
        fields = ['vid','visit_time', 'visitor_count', 'visitor_name', 'visitor_phone', 'maternity_present','appointment_status','appointment_status_display','created_at']
        
    def get_appointment_status_display(self, obj):
        return obj.get_appointment_status_display()
        

# 小程序预约参观序列化器
class WechatAppVisitDetailSerializer(serializers.ModelSerializer):
    # 创建时间
    created_at = ShanghaiFriendlyDateTimeField()   
    # 更新时间
    updated_at = ShanghaiFriendlyDateTimeField()
    # 参观时间
    visit_time = ShanghaiFriendlyDateTimeField()
    # 预约状态
    appointment_status_display = serializers.SerializerMethodField()
    
    class Meta:
        model = WechatAppVisitAppointment
        exclude = ['id','maternity','maternity_center']

    def get_appointment_status_display(self, obj):
        return obj.get_appointment_status_display()


# 小程序预约参观创建序列化器
class WechatAppVisitAppointmentCreateSerializer(serializers.ModelSerializer):
    
    class Meta:
        model = WechatAppVisitAppointment
        fields = ['visit_time', 'visitor_count', 'visitor_name', 'visitor_phone', 'maternity_present','maternity','maternity_center']
        
        

# 消息中心列表序列化器
class WxMessageListSerializer(serializers.ModelSerializer):
    
    # 消息内容
    message_content = serializers.SerializerMethodField()
    # 创建时间
    created_at = ShanghaiFriendlyDateTimeField()
    
    class Meta:
        model = WxMaternityMessage
        fields = ['rid','message_type','message_content','is_read','is_loaded','created_at']
        
    def get_message_content(self,obj):
        return obj.message_content[:50] + '…' if len(obj.message_content) > 50 else obj.message_content
        
        
# 消息中心详情序列化器
class WxMessageDetailSerializer(serializers.ModelSerializer):
    
    # 创建时间
    created_at = ShanghaiFriendlyDateTimeField()
    # 更新时间
    updated_at = ShanghaiFriendlyDateTimeField()
    # 消息类型
    message_type_display = serializers.SerializerMethodField()
    # 发送人
    creator_name = serializers.SerializerMethodField()
    
    class Meta:
        model = WxMaternityMessage
        exclude = ['id','maternity_center','maternity_admission','creator']
        
    def get_message_type_display(self,obj):
        return obj.get_message_type_display()
    
    def get_creator_name(self,obj):
        return obj.creator.name if obj.creator else "N/A"
        
        

# 健康知识列表
class HealthKnowledgeListSerializer(serializers.ModelSerializer):

    class Meta:
        model = HealthKnowledge
        fields = ['rid','title','summary']

# 公共健康知识列表
class PublicHealthKnowledgeListSerializer(serializers.ModelSerializer):

    class Meta:
        model = PublicHealthKnowledge
        fields = ['rid','title','summary']
import logging
from datetime import date, datetime, time

from django.utils import timezone
from rest_framework.views import APIView

from core.authorization import CareCenterAuthentication, HasMaternityPermission
from core.build_record import build_admission_data, build_babies_daily_records_data, build_feeding_data, \
    build_maternity_daily_records_data
from core.enum import CheckInStatusEnum, DeliveryMethodEnum
from core.resp import make_response
from core.utils import calculate_age
from core.view import PaginationListBaseView
from core.wechat.wechat_token_manager import WechatAccessTokenManager
from core.wx_view import WxPaginationListBaseView
from customer_service.activity.models import Activity
from customer_service.activity.serializers import ActivityListSerializer
from customer_service.core_records.models.baby import NewbornCareOneRecord, NewbornCareTwoRecord, \
    NewbornDailyRequiredRecord, Newborn, NewbornFeedingRecord
from customer_service.core_records.models.maternal import MaternityDailyPhysicalCareRecord, \
    MaternityDailyRequiredRecord, MaternityRehabilitationAssessmentRecord
from customer_service.core_records.models.maternity_admission import MaternityAdmission, MaternityRecentActivity
from customer_service.core_records.serializers.baby import NewbornCareOneRecordDetailSerializer, \
    NewbornCareTwoRecordDetailSerializer
from customer_service.core_records.serializers.maternal import MaternityDailyPhysicalCareRecordDetailSerializer, \
    MaternityDailyRequiredRecordDetailSerializer, MaternityRehabilitationAssessmentRecordDetailSerializer
from customer_service.core_records.serializers.maternity_admission import MaternityRecentActivitySerializer
from customer_service.diet.models import MaternityDietRecord
from customer_service.diet.serializers import MaternityDietRecordListSerializer
from customer_service.visitor.enum import WechatAppointmentStatusEnum
from customer_service.visitor.models import WechatAppVisitAppointment
from maternity_center.models import MaternityCenter, MaternityCenterBrandIntroduction, MaternityCenterCarouselFile, MaternityCenterContactUs
from maternity_center.serializers import MaternityCenterBrandIntroductionSerializer, MaternityCenterContactUsSerializer, MaternityCenterListSerializer
from organizational_management.feedback.enum import FeedbackStatusEnum
from organizational_management.feedback.models import MaternityFeedback
from wx_mom.models import UnpaidMaternityPrenatalInfo, WxMaternityMessage, WxUnpaidMaternityMessage
from wx_mom.prenatal_check import generate_prenatal_check_reminders
from wx_mom.serializers import WechatAppVisitAppointmentCreateSerializer, WechatAppVisitListSerializer, WechatAppVisitDetailSerializer, WxBabyListdViewSerializer, WxFeedbackCreateSerializer, WxFeedbackDetailSerializer, WxFeedbackListSerializer, WxFeedbackUpdateSerializer, WxMessageDetailSerializer, WxMessageListSerializer

logger = logging.getLogger(__name__)



# 构建今日日程
def build_today_schedule(admission):

    now = timezone.now()
    today = date.today()

    # 今天的结束时间
    today_end = timezone.make_aware(datetime.combine(today, time.max))

    activities = Activity.objects.filter(
        start_time__gte=now, 
        start_time__lte=today_end,
        maternity_center=admission.maternity_center
    ).order_by('start_time')

    return ActivityListSerializer(activities, many=True).data


# 构建时间大于当前的所有活动
def build_future_schedule(maternity_center):

    today = timezone.now().date()
    today_start = timezone.make_aware(datetime.combine(today, time.min))
    
    print(f'today_start: {today_start}')

    activities = Activity.objects.filter(
        start_time__gte=today_start,
        maternity_center=maternity_center
    ).order_by('start_time')

    activities_by_date = {}
    
    for activity in activities:
        
        activity_date = activity.start_time.date().strftime('%Y-%m-%d')
        if activity_date not in activities_by_date:
            activities_by_date[activity_date] = []

        activity_data = ActivityListSerializer(activity).data
        activities_by_date[activity_date].append(activity_data)

    return activities_by_date



# 获取月子中心列表
class MaternityCenterListView(APIView):
    
    authentication_classes = []
    permission_classes = []

    def get(self, request):
        
        maternity_centers = MaternityCenterListSerializer(MaternityCenter.objects.all(),many=True).data

        return make_response(
            code=0,
            msg="获取月子中心列表成功",
            data=maternity_centers
        )


# 校验产妇和月子中心关系
class MaternityCheckView(APIView):
    
    authentication_classes = [CareCenterAuthentication]
    permission_classes = [HasMaternityPermission]

    def get(self, request, cid):

        try:
            admissions = MaternityAdmission.objects.filter(
                maternity=request.user,
                maternity_center__cid=cid
            ).select_related('room', 'chief_nurse','maternity_center').order_by('-created_at')

            if not admissions.exists():
                return make_response(code=-1, msg="产妇和月子中心关系不存在")

            current_admission = admissions.filter(
                check_in_status__in=[CheckInStatusEnum.RESERVED, CheckInStatusEnum.CHECKED_IN]
            ).first()

            if current_admission:
                history_admissions = admissions.exclude(id=current_admission.id)
            else:
                history_admissions = admissions

            history_records = [build_admission_data(admission) for admission in history_admissions]

            if current_admission:
                if current_admission.check_in_status == CheckInStatusEnum.RESERVED:
                    current_admission_data = build_admission_data(current_admission)

                    return make_response(
                        code=1,
                        msg="产妇当前处于预约状态",
                        data={
                            'status': CheckInStatusEnum.RESERVED,
                            'status_display': '预约中',
                            'current_admission': current_admission_data,
                            'history_records': history_records,
                            'admission_count': admissions.count(),
                            'maternity_center': {
                                'name': current_admission.maternity_center.name,
                                'address': current_admission.maternity_center.address
                            }
                        }
                    )
                elif current_admission.check_in_status == CheckInStatusEnum.CHECKED_IN:
                    current_admission_data = build_admission_data(current_admission)

                    return make_response(
                        code=0,
                        msg="产妇当前正在入住中",
                        data={
                            'status': CheckInStatusEnum.CHECKED_IN,
                            'status_display': '入住中',
                            'current_admission': current_admission_data,
                            'history_records': history_records,
                            'admission_count': admissions.count(),
                            'maternity_center': {
                                'name': current_admission.maternity_center.name,
                                'address': current_admission.maternity_center.address
                            }
                        }
                    )

            latest_admission = admissions.filter(
                check_in_status=CheckInStatusEnum.CHECKED_OUT
            ).first()

            if latest_admission:
                # 情况3：产妇已出院
                # latest_admission_data = build_admission_data(latest_admission)

                return make_response(
                    code=0,
                    msg="产妇已出院",
                    data={
                        'status': CheckInStatusEnum.CHECKED_OUT,
                        'status_display': '已出院',
                        'current_admission': None,
                        'history_records': history_records,
                        'admission_count': admissions.count(),
                        'maternity_center': {
                            'name': latest_admission.maternity_center.name,
                            'address': latest_admission.maternity_center.address
                        }
                    }
                )

            return make_response(code=-1, msg="产妇和月子中心关系不存在")

        except Exception as e:
            logger.error(f"校验产妇关系时发生异常: {e}", exc_info=True)
            return make_response(code=-1, msg="系统异常，请稍后重试")


# 首页总览
class HomeOverviewView(APIView):
    
    authentication_classes = [CareCenterAuthentication]
    permission_classes = [HasMaternityPermission]
    
    def get(self, request, aid):

        try:
            admission = MaternityAdmission.objects.select_related('room', 'chief_nurse').get(
                aid=aid,
                maternity=request.user
            )
        except MaternityAdmission.DoesNotExist:
            return make_response(code=-1, msg="入住记录不存在")

        admission_data = build_admission_data(admission)

        all_maternity_daily_records = MaternityDailyRequiredRecord.objects.filter(
            maternity_admission_id=admission.id
        ).only('id', 'record_date', 'weight', 'temperature', 'blood_pressure').order_by('-record_date')

        all_babies = Newborn.objects.filter(maternity_admission_id=admission.id)

        all_baby_daily_records = NewbornDailyRequiredRecord.objects.filter(
            newborn__maternity_admission_id=admission.id
        ).select_related('newborn').order_by('-record_date')

        maternity_data = build_maternity_daily_records_data(all_maternity_daily_records)

        babies_data = build_babies_daily_records_data(all_baby_daily_records, all_babies)

        overview_data = {
            # 入住记录数据
            'admission_info': admission_data,
            # 产妇数据map，包含最后记录和趋势图
            'maternity_data': maternity_data,
            # 新生儿数据列表
            'babies_data': babies_data,
            # 孩子总数
            'babies_count': len(babies_data),
            # 今日日程
            'today_schedule': build_today_schedule(admission) or []
        }

        return make_response(
            code=0,
            msg="获取首页总览成功",
            data=overview_data
        )
        
        
# 会员详情
class MemberDetailView(APIView):
    
    authentication_classes = [CareCenterAuthentication]
    permission_classes = [HasMaternityPermission]
    
    def get(self, request, aid):
        try:
            admission = MaternityAdmission.objects.select_related('room', 'chief_nurse').prefetch_related('newborns_by_admission').get(
                maternity = request.user,
                aid=aid
            )
        except MaternityAdmission.DoesNotExist:
            return make_response(code=-1, msg="入住记录不存在")
        
        if admission.check_in_status == CheckInStatusEnum.RESERVED:
            return make_response(code=-1, msg="产妇当前处于预约状态,不允许访问会员详情")
        
        detailData = {
            # 入院编号
            'aid': admission.aid,
            # 产妇名字
            'maternity_name': admission.maternity.name,
            # 产妇年龄
            'maternity_age': calculate_age(admission.maternity.birth_date) if admission.maternity.birth_date else None,
            # 房间号
            'room_number': admission.room.room_number if admission.room else None,
            # 入院日期
            'check_in_date': admission.actual_check_in_date if admission.actual_check_in_date else None,
            # 预计退房 
            'expected_check_out_date': admission.expected_check_out_date if admission.expected_check_out_date else None,
            # 入住状态
            'check_in_status': CheckInStatusEnum(admission.check_in_status).label if admission.check_in_status else None,
            # 主责护士
            'chief_nurse_name': admission.chief_nurse.name if admission.chief_nurse else None,
            # 单胞胎还是多胞胎
            'babies_count': admission.newborns_by_admission.count(),
            # 分娩方式
            'delivery_method': DeliveryMethodEnum(admission.delivery_method).label if admission.delivery_method else None,
            # 最近活动
            'recent_activity': MaternityRecentActivitySerializer(MaternityRecentActivity.objects.filter(maternity_admission=admission).order_by('-activity_time')[:3], many=True).data
            
        }
        
        return make_response(
            code=0,
            msg="获取会员详情成功",
            data=detailData
        )


# 产检提醒
class PrenatalCheckView(APIView):
    
    authentication_classes = [CareCenterAuthentication]
    permission_classes = [HasMaternityPermission]
    
    def get(self, request, aid):
        try:
            admission = MaternityAdmission.objects.get(
                aid=aid,
                maternity=request.user
            )
        except MaternityAdmission.DoesNotExist:
            return make_response(code=-1, msg="入住记录不存在")
        
        delivery_date = admission.actual_delivery_date or admission.expected_delivery_date

        if not delivery_date:
            return make_response(code=0, msg="未设置分娩日期，无法生成产检提醒")
        
        prenatal_check_reminders = generate_prenatal_check_reminders(delivery_date)
        
        return make_response(
            code=0,
            msg="获取产检提醒成功",
            data=prenatal_check_reminders
        )


# 活动中心
class ActivityListView(APIView):
    
    authentication_classes = [CareCenterAuthentication]
    permission_classes = [HasMaternityPermission]
    
    def get(self, request,aid):
        
        adms = MaternityAdmission.get_maternity_admission_by_aid(aid,request.user.maternity_center,request.user)
        
        if not adms:
            return make_response(code=-1, msg='入住记录不存在')
        
        return make_response(code=0, msg='获取活动中心列表成功', data=build_future_schedule(adms.maternity_center))

        
# 产妇健康概览
class HealthOverviewView(APIView):
    
    authentication_classes = [CareCenterAuthentication]
    permission_classes = [HasMaternityPermission]
    
    def get(self, request, aid):
        
        try:
            admission = MaternityAdmission.objects.select_related('room', 'chief_nurse').get(
                aid=aid,
                maternity=request.user
            )
        except MaternityAdmission.DoesNotExist:
            return make_response(code=-1, msg="入住记录不存在")

        admission_data = build_admission_data(admission)

        all_maternity_daily_records = MaternityDailyRequiredRecord.objects.filter(
            maternity_admission_id=admission.id
        ).only('id', 'record_date', 'weight', 'temperature', 'blood_pressure').order_by('-record_date')

        maternity_data = build_maternity_daily_records_data(all_maternity_daily_records)


        return make_response(
            code=0,
            msg="获取产妇健康概览成功",
            data=maternity_data
        )
        
        
        
# 产妇膳食
class DietRecordView(APIView):
    
    authentication_classes = [CareCenterAuthentication]
    permission_classes = [HasMaternityPermission]
    
    def get(self, request, aid):    
        
        try:
            maternity_admission = MaternityAdmission.objects.get(aid=aid,maternity=request.user)
        except MaternityAdmission.DoesNotExist:
            return make_response(code=1,msg="产妇入院记录不存在")
        
        
        queryset = MaternityDietRecord.objects.filter(maternity_admission=maternity_admission).order_by('date')
        
        serializer = MaternityDietRecordListSerializer(queryset,many=True)
        
        
        return make_response(code=0,msg="获取产妇膳食记录列表成功",data=serializer.data)
        
        

# 产妇康复护理记录
class RehabilitationRecordView(WxPaginationListBaseView):
    
    authentication_classes = [CareCenterAuthentication]
    permission_classes = [HasMaternityPermission]
    serializer_class = MaternityRehabilitationAssessmentRecordDetailSerializer
    response_msg = "获取产妇康复护理记录列表成功"

    def get_queryset(self,aid):
        
        try:
            maternity_admission = MaternityAdmission.objects.get(aid=aid,maternity=self.request.user)
        except MaternityAdmission.DoesNotExist:
            self.error_response_msg = "产妇入院记录不存在"
            return None
        
        return MaternityRehabilitationAssessmentRecord.objects.filter(maternity_admission=maternity_admission).order_by('-record_date')
        
        
        
# 产妇每日生理护理记录
class PhysicalCareRecordView(WxPaginationListBaseView):
    
    authentication_classes = [CareCenterAuthentication]
    permission_classes = [HasMaternityPermission]
    serializer_class = MaternityDailyPhysicalCareRecordDetailSerializer
    response_msg = "获取产妇每日生理护理记录列表成功"
    
    def get_queryset(self,aid):
        
        try:
            maternity_admission = MaternityAdmission.objects.get(aid=aid,maternity=self.request.user)
        except MaternityAdmission.DoesNotExist:
            self.error_response_msg = "产妇入院记录不存在"
            return None
        
        return MaternityDailyPhysicalCareRecord.objects.filter(maternity_admission=maternity_admission).order_by('-record_date')
        


# 产妇每日必填记录
class DailyRequiredRecordView(WxPaginationListBaseView):
    
    authentication_classes = [CareCenterAuthentication]
    permission_classes = [HasMaternityPermission]
    serializer_class = MaternityDailyRequiredRecordDetailSerializer
    response_msg = "获取产妇每日必填记录列表成功"
    
    def get_queryset(self,aid):
        
        try:
            maternity_admission = MaternityAdmission.objects.get(aid=aid,maternity=self.request.user)
        except MaternityAdmission.DoesNotExist:
            self.error_response_msg = "产妇入院记录不存在"
            return None
        
        return MaternityDailyRequiredRecord.objects.filter(maternity_admission=maternity_admission).order_by('-record_date')
        
        
        
        
# 婴儿健康记录
class BabyListdView(WxPaginationListBaseView):
    
    authentication_classes = [CareCenterAuthentication]
    permission_classes = [HasMaternityPermission]
    serializer_class = WxBabyListdViewSerializer
    response_msg = "获取婴儿健康记录列表成功"
    
    def get_queryset(self,aid):
        
        try:
            maternity_admission = MaternityAdmission.objects.get(aid=aid,maternity=self.request.user)
        except MaternityAdmission.DoesNotExist:
            self.error_response_msg = "产妇入院记录不存在"
            return None

        from django.db.models import Prefetch
        from customer_service.core_records.models.baby import NewbornFeedingRecord, NewbornCareOneRecord, NewbornCareTwoRecord, NewbornDailyRequiredRecord

        return Newborn.objects.prefetch_related(
            Prefetch('newborn_feeding_records', queryset=NewbornFeedingRecord.objects.order_by('-record_time')),
            Prefetch('newborn_care_one_records', queryset=NewbornCareOneRecord.objects.order_by('-record_date')),
            Prefetch('newborn_care_two_records', queryset=NewbornCareTwoRecord.objects.order_by('-record_date')),
            Prefetch('newborn_daily_required', queryset=NewbornDailyRequiredRecord.objects.select_related('creator').order_by('-record_date'))
        ).filter(maternity_admission=maternity_admission).order_by('-birth_time')
        
        
# 婴儿喂养记录
class BabyFeedingRecordView(APIView):
    
    authentication_classes = [CareCenterAuthentication]
    permission_classes = [HasMaternityPermission]
    
    def get(self,request,nid):
        
        try:
            newborn = Newborn.objects.get(nid=nid,maternity_admission__maternity=request.user)
        except Newborn.DoesNotExist:
            return make_response(code=-1,msg="婴儿不存在")
        
        queryset = NewbornFeedingRecord.objects.filter(newborn=newborn).order_by('-record_time')
        
        result = build_feeding_data(queryset)
        
        return make_response(code=0,msg="获取婴儿喂养记录列表成功",data=result)
    
    

# 婴儿每日护理记录
class BabyDailyCareRecordView(APIView):
    
    authentication_classes = [CareCenterAuthentication]
    permission_classes = [HasMaternityPermission]
    
    def get(self,request,nid):
        
        try:
            newborn = Newborn.objects.get(nid=nid,maternity_admission__maternity=request.user)
        except Newborn.DoesNotExist:
            return make_response(code=-1,msg="婴儿不存在")
        
        queryset = NewbornCareOneRecord.objects.filter(newborn=newborn).order_by('-record_date')
        
        result = NewbornCareOneRecordDetailSerializer(queryset,many=True).data
        
        return make_response(code=0,msg="获取婴儿每日护理记录列表成功",data=result)
        

# 每日体格检查记录
class BabyPhysicalExaminationRecordView(APIView):
    
    authentication_classes = [CareCenterAuthentication]
    permission_classes = [HasMaternityPermission]
    
    def get(self,request,nid):
        
        try:
            newborn = Newborn.objects.get(nid=nid,maternity_admission__maternity=request.user)
        except Newborn.DoesNotExist:
            return make_response(code=-1,msg="婴儿不存在")
        
        queryset = NewbornCareTwoRecord.objects.filter(newborn=newborn).order_by('-record_date')
        
        result = NewbornCareTwoRecordDetailSerializer(queryset,many=True).data
        
        return make_response(code=0,msg="获取婴儿每日护理记录列表成功",data=result)
    
    
    

# 意见反馈列表
class FeedbackListView(WxPaginationListBaseView):
    
    authentication_classes = [CareCenterAuthentication]
    permission_classes = [HasMaternityPermission]

    def get(self,request,aid):
        
        admission = MaternityAdmission.get_maternity_admission_by_aid(aid,request.user.maternity_center,request.user)

        if not admission:
            return make_response(code=-1,msg="产妇入院记录不存在")

        queryset = MaternityFeedback.get_feedback_queryset_by_aid(aid)

        serializer = WxFeedbackListSerializer(queryset,many=True)

        return make_response(code=0,msg="获取意见反馈列表成功",data=serializer.data)


# 意见反馈详情
class FeedbackDetailView(APIView):
    
    authentication_classes = [CareCenterAuthentication]
    permission_classes = [HasMaternityPermission]
    
    def get(self,request,rid):
        
        feedback = MaternityFeedback.get_feedback_by_fid(fid=rid,maternity=request.user,maternity_center=request.user.maternity_center)

        if not feedback:
            return make_response(code=-1,msg="意见反馈不存在")

        serializer = WxFeedbackDetailSerializer(feedback)
        
        return make_response(code=0,msg="获取意见反馈详情成功",data=serializer.data)    
        
        
    
    
# 创建意见反馈
class FeedbackCreateView(APIView):
    
    authentication_classes = [CareCenterAuthentication]
    permission_classes = [HasMaternityPermission]
    
    def post(self,request,aid):
        
        data = request.data.copy()

        
        admission = MaternityAdmission.get_maternity_admission_by_aid(aid,request.user.maternity_center,request.user)

        if not admission:
            return make_response(code=-1,msg="产妇入院记录不存在")
        
        data['maternity_admission'] = admission.id
        data['maternity_center'] = request.user.maternity_center.id
        
        serializer = WxFeedbackCreateSerializer(data=data)
        
        if serializer.is_valid():
            serializer.save()
            return make_response(code=0,msg="创建意见反馈成功",data=WxFeedbackDetailSerializer(serializer.instance).data)
        else:
            return make_response(code=-1,msg="数据校验失败")
        
    
    
# 更新意见反馈
class FeedbackUpdateView(APIView):
    
    authentication_classes = [CareCenterAuthentication]
    permission_classes = [HasMaternityPermission]
    
    def put(self,request,rid):
        data = request.data.copy()
        
        feedback = MaternityFeedback.get_feedback_by_fid(fid=rid,maternity=request.user,maternity_center=request.user.maternity_center)

        if not feedback:
            return make_response(code=-1,msg="意见反馈不存在")
        
        if feedback.status != FeedbackStatusEnum.PENDING:
            return make_response(code=-1,msg=f"意见反馈当前状态为：{feedback.get_status_display()}，无法更新")
        
        serializer = WxFeedbackUpdateSerializer(feedback,data=data)
        
        if serializer.is_valid():
            serializer.save()
            return make_response(code=0,msg="更新意见反馈成功",data=WxFeedbackDetailSerializer(serializer.instance).data)
        else:
            return make_response(code=-1,msg="数据校验失败")
    
    
# 删除意见反馈
class FeedbackDeleteView(APIView):
    
    authentication_classes = [CareCenterAuthentication]
    permission_classes = [HasMaternityPermission]
    
    def delete(self,request,rid):
        
        feedback = MaternityFeedback.get_feedback_by_fid(fid=rid,maternity=request.user,maternity_center=request.user.maternity_center)
        
        if not feedback:
            return make_response(code=-1,msg="意见反馈不存在")
        
        if feedback.status != FeedbackStatusEnum.PENDING:
            return make_response(code=-1,msg=f"意见反馈当前状态为：{feedback.get_status_display()}，无法删除")
        
        feedback.delete()
        
        return make_response(code=0,msg="删除意见反馈成功")


# 消息中心列表
class MessageListView(WxPaginationListBaseView):
    
    authentication_classes = [CareCenterAuthentication]
    permission_classes = [HasMaternityPermission]
    serializer_class = WxMessageListSerializer
    response_msg = "获取消息中心列表成功"
    search_fields = ['message_type']

    def get_queryset(self,aid):
        
        try:
            maternity_admission = MaternityAdmission.objects.get(aid=aid,maternity=self.request.user)
        except MaternityAdmission.DoesNotExist:
            self.error_response_msg = "产妇入院记录不存在"
            return None
        
        return WxMaternityMessage.objects.filter(maternity_admission=maternity_admission).order_by('-created_at')

    def update_fields(self,queryset):
        queryset.filter(is_loaded=False).update(is_loaded=True)


# 消息中心详情
class MessageDetailView(APIView):
    
    authentication_classes = [CareCenterAuthentication]
    permission_classes = [HasMaternityPermission]
    
    def get(self,request,rid):

        message = WxMaternityMessage.get_message_by_rid(rid=rid,user=request.user)
        
        if not message:
            return make_response(code=-1,msg="消息不存在")
        
        message.mark_message_as_read()
        
        return make_response(code=0,msg="获取消息中心详情成功",data=WxMessageDetailSerializer(message).data)


# 消息轮询
class MessagePollView(APIView):
    
    authentication_classes = [CareCenterAuthentication]
    permission_classes = [HasMaternityPermission]
    
    def get(self,request):
        
        queryset = WxMaternityMessage.objects.filter(maternity_admission__maternity=request.user,is_loaded=False).order_by('-created_at')
                
        count = queryset.count()
        
        queryset.filter(is_loaded=False).update(is_loaded=True)
        
        return make_response(code=0,msg="Success",data={'has_new':count > 0,'count':count})


# 小程序预约参观列表
class WechatAppVisitListView(APIView):
    
    authentication_classes = [CareCenterAuthentication]
    permission_classes = [HasMaternityPermission]

    def get(self, request):
        appointments = WechatAppVisitAppointment.get_wechat_visit_queryset_by_vid(user=request.user).order_by('-created_at')
        serializer = WechatAppVisitListSerializer(appointments, many=True)
        return make_response(code=0, msg='获取预约参观列表成功', data=serializer.data)
    

# 小程序预约参观详情
class WechatAppVisitDetailView(APIView):
    
    authentication_classes = [CareCenterAuthentication]
    permission_classes = [HasMaternityPermission]
    
    def get(self, request, vid):
        
        appointment = WechatAppVisitAppointment.get_wechat_visit_by_vid(vid=vid,user=request.user)
        
        if not appointment:
            return make_response(code=-1, msg='预约不存在')
        
        serializer = WechatAppVisitDetailSerializer(appointment)
        
        return make_response(code=0, msg='获取预约参观详情成功', data=serializer.data)
    
    
        
# 创建小程序预约参观
class WechatAppVisitCreateView(APIView):
    
    authentication_classes = [CareCenterAuthentication]
    permission_classes = [HasMaternityPermission]

    def post(self, request):
        
        data = request.data.copy()
        
        if not data['visit_time']:
            return make_response(code=-1, msg='请选择参观时间')
        
        if WechatAppVisitAppointment.check_has_same_day_visit(user=request.user,visit_time=data['visit_time']):
            return make_response(code=-1, msg='您已预约过同一天的参观，请勿重复预约')
        
        data['maternity'] = request.user.id
        data['maternity_center'] = request.user.maternity_center.id
        
        serializer = WechatAppVisitAppointmentCreateSerializer(data=data)
        if serializer.is_valid():
            appointment = serializer.save()
            appointment_serializer = WechatAppVisitDetailSerializer(appointment)
            return make_response(code=0, msg='预约成功，待审核通过后会通过小程序通知您', data=appointment_serializer.data)
        return make_response(code=-1, msg=serializer.errors)
    
    
# 取消小程序预约参观
class WechatAppVisitCancelView(APIView):
    
    authentication_classes = [CareCenterAuthentication]
    permission_classes = [HasMaternityPermission]

    def put(self, request, vid):
        try:
            appointment = WechatAppVisitAppointment.get_wechat_visit_by_vid(vid=vid,user=request.user)
            
            if not appointment:
                return make_response(code=-1, msg='预约不存在')
            
            if appointment.appointment_status not in [WechatAppointmentStatusEnum.PENDING, WechatAppointmentStatusEnum.CONFIRMED]:
                return make_response(code=-1, msg=f'预约无法取消,当前状态为:{appointment.get_appointment_status_display()}')
            
            appointment.appointment_status = WechatAppointmentStatusEnum.CANCELLED
            appointment.save()
            
            return make_response(code=0, msg=f'您已成功取消 {appointment.get_visitor_time()} 的预约参观申请')
        
        except WechatAppVisitAppointment.DoesNotExist:
            return make_response(code=-1, msg='预约不存在')
        


# 未付费用户产检提醒信息
class UnpaidPrenatalInfoView(APIView):
    
    authentication_classes = [CareCenterAuthentication]
    permission_classes = [HasMaternityPermission]
    
    def get(self, request):

        umpi = UnpaidMaternityPrenatalInfo.get_unpaid_maternity_prenatal_info(user=request.user)
        
        if not umpi:
            return make_response(code=1, msg='您未创建过产检提醒信息')
        
        prenatal_check_reminders = generate_prenatal_check_reminders(expected_delivery_date=umpi.expected_delivery_date)
        
        return make_response(code=0, msg='获取产检提醒信息成功', data=prenatal_check_reminders)
        

# 未付费用户产检提醒创建
class UnpaidPrenatalCreateView(APIView):
    
    authentication_classes = [CareCenterAuthentication]
    permission_classes = [HasMaternityPermission]
    
    def post(self, request):
        
        umpi = UnpaidMaternityPrenatalInfo.get_unpaid_maternity_prenatal_info(user=request.user)
        
        if umpi:
            return make_response(code=-1, msg='您已创建过产检提醒信息')
        
        data = request.data.copy()
                
        expected_delivery_date = data.get('edd')
        
        if not expected_delivery_date:
            return make_response(code=-1, msg='请选择预计分娩日期')
        
        UnpaidMaternityPrenatalInfo.create_unpaid_maternity_prenatal_info(user=request.user, expected_delivery_date=expected_delivery_date)
        
        prenatal_check_reminders = generate_prenatal_check_reminders(expected_delivery_date=expected_delivery_date)
        
        return make_response(
            code=0,
            msg="创建产检提醒信息成功",
            data=prenatal_check_reminders
        )
        
        
# 未付费用户产检提醒更新
class UnpaidPrenatalUpdateView(APIView):
    
    authentication_classes = [CareCenterAuthentication]
    permission_classes = [HasMaternityPermission]
    
    def put(self, request):
        
        umpi = UnpaidMaternityPrenatalInfo.get_unpaid_maternity_prenatal_info(user=request.user)
        
        if not umpi:
            return make_response(code=-1, msg='您未创建过产检提醒信息')
        
        data = request.data.copy()
        
        expected_delivery_date = data.get('edd')
        
        if not expected_delivery_date:
            return make_response(code=-1, msg='请选择预计分娩日期')
        
        umpi.update_expected_delivery_date(expected_delivery_date=expected_delivery_date)
        
        prenatal_check_reminders = generate_prenatal_check_reminders(expected_delivery_date=expected_delivery_date)
        
        return make_response(
            code=0,
            msg="更新产检提醒信息成功",
            data=prenatal_check_reminders
        )        
        
        

# 未付费会员消息中心列表
class UnpaidMessageListView(PaginationListBaseView):
    
    authentication_classes = [CareCenterAuthentication]
    permission_classes = [HasMaternityPermission]
    serializer_class = WxMessageListSerializer
    response_msg = "获取未付费会员消息中心列表成功"
    search_fields = ['message_type']

    def get_queryset(self):
        
        return WxUnpaidMaternityMessage.objects.filter(maternity=self.request.user).order_by('-created_at')
    
    def update_fields(self,queryset):
        queryset.filter(is_loaded=False).update(is_loaded=True)


# 未付费会员消息中心详情
class UnpaidMessageDetailView(APIView):
    
    authentication_classes = [CareCenterAuthentication]
    permission_classes = [HasMaternityPermission]
    
    def get(self,request,rid):

        message = WxUnpaidMaternityMessage.get_message_by_rid(rid=rid,user=request.user)
        
        if not message:
            return make_response(code=-1,msg="消息不存在")
        
        message.mark_message_as_read()
        
        return make_response(code=0,msg="获取未付费会员消息中心详情成功",data=WxMessageDetailSerializer(message).data)
    
    

# 未付费会员消息轮询
class UnpaidMessagePollView(APIView):
    
    authentication_classes = [CareCenterAuthentication]
    permission_classes = [HasMaternityPermission]
    
    def get(self,request):
        
        queryset = WxUnpaidMaternityMessage.objects.filter(maternity=request.user,is_loaded=False).order_by('-created_at')
                
        count = queryset.count()
        
        queryset.filter(is_loaded=False).update(is_loaded=True)
        
        return make_response(code=0,msg="Success",data={'has_new':count > 0,'count':count})

    
    

# 获取月子中心轮播图列表
class MaternityCenterCarouselListView(APIView):
    authentication_classes = []
    permission_classes = []

    def get(self, request,cid):
        csfs = MaternityCenterCarouselFile.objects.filter(maternity_center__cid=cid)
        resData = []
        for csf in csfs:
            if csf.file:
                file_url = csf.file.url
                if file_url:
                    resData.append(file_url)
        return make_response(msg='获取月子中心轮播图列表成功',data=resData)

# 获取月子中心品牌介绍列表
class MaternityCenterBrandIntroductionListView(APIView):
    authentication_classes = []
    permission_classes = []

    def get(self, request,cid):
        mci = MaternityCenterBrandIntroduction.objects.filter(maternity_center__cid=cid)
        serializer = MaternityCenterBrandIntroductionSerializer(mci, many=True)
        return make_response(msg='获取月子中心品牌介绍成功',data=serializer.data)
    
# 获取月子中心联系信息列表
class MaternityCenterContactUsListView(APIView):
    authentication_classes = []
    permission_classes = []

    def get(self, request,cid):
        mci = MaternityCenterContactUs.objects.filter(maternity_center__cid=cid)
        serializer = MaternityCenterContactUsSerializer(mci, many=True)
        return make_response(msg='获取月子中心联系信息成功',data=serializer.data)